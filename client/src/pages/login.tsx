import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/hooks/use-auth";
import { login } from "@/lib/auth";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const { setUser } = useAuth();
  const { toast } = useToast();

  const loginMutation = useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      login(email, password),
    onSuccess: (data) => {
      setUser(data.user);
      toast({
        title: "Welcome to DPrinceJ College",
        description: `Logged in as ${data.user.fullName}`,
      });
    },
    onError: () => {
      toast({
        title: "Lo<PERSON> failed",
        description: "Invalid credentials. Please use the demo accounts provided.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate({ email, password });
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-dpj-orange rounded-full flex items-center justify-center mb-4">
            <span className="text-white text-2xl font-bold">DJ</span>
          </div>
          <h2 className="text-3xl font-bold text-gray-900">DPrinceJ College</h2>
          <p className="mt-2 text-sm text-gray-600">School Management Portal</p>
        </div>
        
        <Card>
          <CardHeader>
            <div className="text-sm text-gray-600">
              <p className="font-medium text-gray-900 mb-2">Demo Accounts:</p>
              <div className="space-y-1">
                <p><span className="font-medium">Parent:</span> daddy / mummy</p>
                <p><span className="font-medium">Teacher:</span> headmaster / jss6334</p>
                <p><span className="font-medium">Admin:</span> administrator / okokomaiko</p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="email">Email/Username</Label>
                <Input
                  id="email"
                  type="text"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your username"
                  required
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full bg-dpj-orange hover:bg-dpj-orange-dark"
                disabled={loginMutation.isPending}
              >
                {loginMutation.isPending ? "Signing in..." : "Sign In"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
