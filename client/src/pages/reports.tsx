import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Download, FileText, TrendingUp } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useState } from "react";
import type { Student, Class as ClassType } from "@shared/schema";

export default function Reports() {
  const { user } = useAuth();
  const [selectedStudent, setSelectedStudent] = useState("");
  const [selectedClass, setSelectedClass] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("term");

  const { data: students } = useQuery<Student[]>({
    queryKey: user?.role === "parent" ? [`/api/students/parent/${user.id}`] : ["/api/students"],
    enabled: !!user,
  });

  const { data: classes } = useQuery<ClassType[]>({
    queryKey: ["/api/classes"],
  });

  // Sample academic performance data
  const academicPerformance = [
    { subject: "Mathematics", testScore: "85/100", grade: "A", attendance: "95%" },
    { subject: "English Language", testScore: "78/100", grade: "B", attendance: "98%" },
    { subject: "Science", testScore: "92/100", grade: "A", attendance: "100%" },
  ];

  // Sample teacher comments
  const teacherComments = [
    {
      subject: "Mathematics",
      teacher: "Mrs. Johnson",
      comment: "Adebayo shows excellent understanding of mathematical concepts and consistently produces high-quality work. He actively participates in class discussions and helps other students."
    },
    {
      subject: "English Language", 
      teacher: "Mr. Adams",
      comment: "Good progress in reading comprehension and writing skills. Adebayo should focus on expanding his vocabulary and practicing essay writing structure."
    }
  ];

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Student Reports</h2>
        <p className="text-gray-600 mt-1">Generate and view comprehensive student performance reports</p>
      </div>

      {/* Report Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-dpj-orange" />
            Generate Report
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Student</Label>
              <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                <SelectTrigger>
                  <SelectValue placeholder={user?.role === "parent" ? "Select your child" : "All Students"} />
                </SelectTrigger>
                <SelectContent>
                  {user?.role !== "parent" && <SelectItem value="all">All Students</SelectItem>}
                  {students?.map((student) => (
                    <SelectItem key={student.id} value={student.id.toString()}>
                      {student.fullName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="All Classes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Classes</SelectItem>
                  {classes?.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Period</Label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="term">This Term</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full bg-dpj-orange hover:bg-dpj-orange-dark">
                Generate Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sample Student Report */}
      <Card>
        <CardHeader className="border-b">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl">Student Performance Report</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Adebayo Benjamin • Grade 9-A • First Term 2023/2024
              </p>
            </div>
            <Button className="bg-dpj-orange hover:bg-dpj-orange-dark">
              <Download className="w-4 h-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Academic Performance */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-dpj-orange" />
              Academic Performance
            </h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Subject</TableHead>
                  <TableHead className="text-center">Test Score</TableHead>
                  <TableHead className="text-center">Grade</TableHead>
                  <TableHead className="text-center">Attendance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {academicPerformance.map((performance, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{performance.subject}</TableCell>
                    <TableCell className="text-center">{performance.testScore}</TableCell>
                    <TableCell className="text-center">
                      <Badge 
                        variant={performance.grade === 'A' ? 'default' : 'secondary'}
                        className={performance.grade === 'A' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}
                      >
                        {performance.grade}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">{performance.attendance}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Teacher Comments */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Teacher Comments</h4>
            <div className="space-y-4">
              {teacherComments.map((comment, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h5 className="font-medium text-gray-900">{comment.subject}</h5>
                    <span className="text-xs text-gray-500">{comment.teacher}</span>
                  </div>
                  <p className="text-sm text-gray-700">{comment.comment}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Overall Summary */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-2">Overall Assessment</h4>
            <p className="text-sm text-gray-700">
              Adebayo is a dedicated and bright student who consistently performs well across all subjects. 
              His attendance is excellent and his attitude towards learning is commendable. He should continue 
              his current study habits while focusing on areas for improvement noted by individual subject teachers.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Empty State for Parent with No Children */}
      {user?.role === "parent" && (!students || students.length === 0) && (
        <Card>
          <CardContent className="py-12 text-center">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No students found</h3>
            <p className="text-gray-600">No children are associated with your parent account.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
