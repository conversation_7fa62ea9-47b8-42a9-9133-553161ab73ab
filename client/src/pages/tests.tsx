import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, FileText, Calendar, Users, BarChart } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import type { Test, Class as ClassType, Subject } from "@shared/schema";

export default function Tests() {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const { data: tests, isLoading } = useQuery<Test[]>({
    queryKey: ["/api/tests"],
  });

  const { data: classes } = useQuery<ClassType[]>({
    queryKey: ["/api/classes"],
  });

  const { data: subjects } = useQuery<Subject[]>({
    queryKey: ["/api/subjects"],
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Test Management</h2>
            <p className="text-gray-600 mt-1">Create and manage tests and grading</p>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-32 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Test Management</h2>
          <p className="text-gray-600 mt-1">Create and manage tests and grading</p>
        </div>
        <Button 
          className="bg-dpj-orange hover:bg-dpj-orange-dark"
          onClick={() => setShowCreateForm(!showCreateForm)}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Test
        </Button>
      </div>

      {/* Test Creation Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="testTitle">Test Title</Label>
                <Input
                  id="testTitle"
                  placeholder="e.g., Mid-term Mathematics Test"
                />
              </div>
              <div>
                <Label>Subject</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects?.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>
                        {subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Class</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes?.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id.toString()}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="testDate">Test Date</Label>
                <Input
                  id="testDate"
                  type="date"
                />
              </div>
              <div>
                <Label htmlFor="totalScore">Total Score</Label>
                <Input
                  id="totalScore"
                  type="number"
                  placeholder="100"
                />
              </div>
              <div>
                <Label>Grade Scale</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select grade scale" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard (A:80+, B:70+, C:60+, D:50+, F:0+)</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="mt-6 flex space-x-2">
              <Button className="bg-dpj-orange hover:bg-dpj-orange-dark">
                Create Test
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tests List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {tests?.map((test) => (
          <Card key={test.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{test.title}</CardTitle>
                  <p className="text-sm text-gray-600">
                    {subjects?.find(s => s.id === test.subjectId)?.name} • {classes?.find(c => c.id === test.classId)?.name}
                  </p>
                </div>
                <Badge className="bg-green-100 text-green-800">Completed</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>Date:</span>
                  </div>
                  <span className="font-medium">{new Date(test.date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <BarChart className="w-4 h-4 mr-2" />
                    <span>Total Score:</span>
                  </div>
                  <span className="font-medium">{test.totalScore}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <Users className="w-4 h-4 mr-2" />
                    <span>Students:</span>
                  </div>
                  <span className="font-medium">0</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <BarChart className="w-4 h-4 mr-2" />
                    <span>Average:</span>
                  </div>
                  <span className="font-medium">-</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  className="flex-1 border-dpj-orange text-dpj-orange hover:bg-dpj-orange hover:text-white"
                >
                  View Results
                </Button>
                <Button variant="outline" className="flex-1">
                  Edit Scores
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Pending Test Example */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-4">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">English Essay Test</CardTitle>
                <p className="text-sm text-gray-600">English Language • Grade 8-B</p>
              </div>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>Date:</span>
                </div>
                <span className="font-medium">Jan 18, 2024</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <BarChart className="w-4 h-4 mr-2" />
                  <span>Total Score:</span>
                </div>
                <span className="font-medium">50</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <Users className="w-4 h-4 mr-2" />
                  <span>Students:</span>
                </div>
                <span className="font-medium">28</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <FileText className="w-4 h-4 mr-2" />
                  <span>Graded:</span>
                </div>
                <span className="font-medium">15/28</span>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                className="flex-1 border-dpj-orange text-dpj-orange hover:bg-dpj-orange hover:text-white"
              >
                Continue Grading
              </Button>
              <Button variant="outline" className="flex-1">
                Edit Test
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {tests?.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tests found</h3>
            <p className="text-gray-600 mb-4">Create your first test to get started.</p>
            <Button 
              className="bg-dpj-orange hover:bg-dpj-orange-dark"
              onClick={() => setShowCreateForm(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Test
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
