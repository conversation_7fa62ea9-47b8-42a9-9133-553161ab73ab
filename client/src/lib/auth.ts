import { apiRequest } from "./queryClient";
import type { User } from "@shared/schema";

export interface AuthUser {
  id: number;
  fullName: string;
  email: string;
  role: "admin" | "teacher" | "parent";
}

export interface LoginResponse {
  user: AuthUser;
}

export async function login(email: string, password: string): Promise<LoginResponse> {
  const response = await apiRequest("POST", "/api/login", { email, password });
  return response.json();
}

export async function logout(): Promise<void> {
  await apiRequest("POST", "/api/logout");
}

export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    const response = await apiRequest("GET", "/api/me");
    const data = await response.json();
    return data.user;
  } catch (error: any) {
    if (error.message.includes("401")) {
      return null;
    }
    throw error;
  }
}
