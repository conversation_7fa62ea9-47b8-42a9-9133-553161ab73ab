@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(20, 100%, 60%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* DPrinceJ College Custom Colors */
  --dpj-orange: hsl(20, 100%, 60%);
  --dpj-orange-light: hsl(20, 100%, 70%);
  --dpj-orange-dark: hsl(20, 100%, 50%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(20, 100%, 60%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* DPrinceJ College Custom Colors - Dark Mode */
  --dpj-orange: hsl(20, 100%, 60%);
  --dpj-orange-light: hsl(20, 100%, 70%);
  --dpj-orange-dark: hsl(20, 100%, 50%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .dpj-orange {
    color: var(--dpj-orange);
  }
  
  .bg-dpj-orange {
    background-color: var(--dpj-orange);
  }
  
  .bg-dpj-orange-light {
    background-color: var(--dpj-orange-light);
  }
  
  .bg-dpj-orange-dark {
    background-color: var(--dpj-orange-dark);
  }
  
  .border-dpj-orange {
    border-color: var(--dpj-orange);
  }
  
  .hover\:bg-dpj-orange-dark:hover {
    background-color: var(--dpj-orange-dark);
  }
  
  .focus\:ring-dpj-orange:focus {
    --tw-ring-color: var(--dpj-orange);
  }
  
  .focus\:border-dpj-orange:focus {
    border-color: var(--dpj-orange);
  }
}
