import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";

interface NavigationProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export function Navigation({ activeSection, onSectionChange }: NavigationProps) {
  const { user } = useAuth();

  const navItems = [
    { id: "dashboard", label: "Dashboard", roles: ["admin", "teacher", "parent"] },
    { id: "students", label: "Students", roles: ["admin", "teacher"] },
    { id: "classes", label: "Classes", roles: ["admin", "teacher"] },
    { id: "attendance", label: "Attendance", roles: ["admin", "teacher"] },
    { id: "tests", label: "Tests", roles: ["admin", "teacher"] },
    { id: "reports", label: "Reports", roles: ["admin", "teacher", "parent"] },
  ];

  const visibleItems = navItems.filter(item => 
    user?.role && item.roles.includes(user.role)
  );

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex space-x-8 overflow-x-auto">
          {visibleItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={cn(
                "border-b-2 py-4 px-1 text-sm font-medium whitespace-nowrap transition-colors",
                activeSection === item.id
                  ? "border-dpj-orange text-dpj-orange"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              {item.label}
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
}
