Here is a detailed specification document for your **mobile-first school management web application**. It includes functional requirements, system roles, and a comprehensive database schema designed for SQLite3. It’s structured to be developer-friendly for implementation with frameworks like Flask, FastAPI, or Express, and styled using Tailwind CSS.

---

## 🌐 Project Title: School Management Web Portal

### 🎯 Objective

To build a mobile-first web application that enables schools to efficiently manage students, teachers, classes, subjects, tests, and parent interactions — with clearly defined roles and permissions.

---

## 👥 User Roles

| Role        | Description                                                                                               |
| ----------- | --------------------------------------------------------------------------------------------------------- |
| **Admin**   | Manages school configuration, user accounts, classes, and academic structure.                             |
| **Teacher** | Manages subjects, uploads course materials, creates tests, tracks attendance, and writes student reports. |
| **Parent**  | Views student performance, attendance, and reports for their children.                                    |
| **Student** | Can view their own grades and materials (optional for v1).                                                |

---

## 🔐 Authentication

* **Login system** with bcrypt-hashed passwords
* **Role-based access control**
* **No 2FA** (for simplicity)

---

## 📘 Key Features

### 1. **User Management**

* Account creation (admin only)
* Password storage with **bcrypt**
* Role assignment at registration

### 2. **Student Management**

* Registration and personal info
* Class enrollments
* Attendance records
* Viewable test results & teacher reports

### 3. **Teacher Management**

* Assign subjects
* Upload materials (PDFs, links, notes)
* Create and grade tests
* Set grading scales
* Write subjective performance reports

### 4. **Subject & Class Management**

* Subjects can belong to multiple classes
* Students can be enrolled in multiple subjects via class enrollment

### 5. **Test & Grading System**

* Create tests with max score
* Set grade scale per test
* Auto-grade students based on score ranges
* Store detailed test records

### 6. **Parent Portal**

* View all children registered
* View class attendance, performance, and reports
* Secure login with limited access

---

## 🗄️ Database Schema (SQLite3)

Below is the proposed schema with tables and essential fields:

### 1. `users`

| Field          | Type                                             | Notes |
| -------------- | ------------------------------------------------ | ----- |
| id             | INTEGER PRIMARY KEY                              |       |
| full\_name     | TEXT                                             |       |
| email          | TEXT UNIQUE                                      |       |
| password\_hash | TEXT (bcrypt)                                    |       |
| role           | TEXT CHECK(role IN ('admin','teacher','parent')) |       |
| created\_at    | DATETIME DEFAULT CURRENT\_TIMESTAMP              |       |

---

### 2. `students`

| Field              | Type                         | Notes |
| ------------------ | ---------------------------- | ----- |
| id                 | INTEGER PRIMARY KEY          |       |
| full\_name         | TEXT                         |       |
| dob                | DATE                         |       |
| gender             | TEXT                         |       |
| registration\_date | DATETIME                     |       |
| parent\_id         | INTEGER REFERENCES users(id) |       |
| is\_active         | BOOLEAN DEFAULT 1            |       |

---

### 3. `teachers`

| Field      | Type                         | Notes |
| ---------- | ---------------------------- | ----- |
| id         | INTEGER PRIMARY KEY          |       |
| user\_id   | INTEGER REFERENCES users(id) |       |
| staff\_id  | TEXT UNIQUE                  |       |
| hire\_date | DATETIME                     |       |

---

### 4. `classes`

| Field          | Type                | Notes               |
| -------------- | ------------------- | ------------------- |
| id             | INTEGER PRIMARY KEY |                     |
| name           | TEXT                | e.g., "Grade 9 - A" |
| academic\_year | TEXT                |                     |

---

### 5. `subjects`

| Field       | Type                            | Notes               |
| ----------- | ------------------------------- | ------------------- |
| id          | INTEGER PRIMARY KEY             |                     |
| name        | TEXT                            | e.g., "Mathematics" |
| description | TEXT                            |                     |
| teacher\_id | INTEGER REFERENCES teachers(id) |                     |

---

### 6. `class_enrollments`

| Field        | Type                            | Notes |
| ------------ | ------------------------------- | ----- |
| id           | INTEGER PRIMARY KEY             |       |
| student\_id  | INTEGER REFERENCES students(id) |       |
| class\_id    | INTEGER REFERENCES classes(id)  |       |
| enrolled\_on | DATETIME                        |       |

---

### 7. `subject_classes`

| Field       | Type                            | Notes |
| ----------- | ------------------------------- | ----- |
| id          | INTEGER PRIMARY KEY             |       |
| class\_id   | INTEGER REFERENCES classes(id)  |       |
| subject\_id | INTEGER REFERENCES subjects(id) |       |

---

### 8. `attendance`

| Field       | Type                                                 | Notes |
| ----------- | ---------------------------------------------------- | ----- |
| id          | INTEGER PRIMARY KEY                                  |       |
| student\_id | INTEGER REFERENCES students(id)                      |       |
| subject\_id | INTEGER REFERENCES subjects(id)                      |       |
| class\_id   | INTEGER REFERENCES classes(id)                       |       |
| date        | DATE                                                 |       |
| status      | TEXT CHECK(status IN ('present','absent','excused')) |       |

---

### 9. `materials`

| Field        | Type                            | Notes |
| ------------ | ------------------------------- | ----- |
| id           | INTEGER PRIMARY KEY             |       |
| subject\_id  | INTEGER REFERENCES subjects(id) |       |
| teacher\_id  | INTEGER REFERENCES teachers(id) |       |
| title        | TEXT                            |       |
| file\_url    | TEXT (optional)                 |       |
| notes        | TEXT                            |       |
| uploaded\_at | DATETIME                        |       |

---

### 10. `tests`

| Field        | Type                            | Notes                                                     |
| ------------ | ------------------------------- | --------------------------------------------------------- |
| id           | INTEGER PRIMARY KEY             |                                                           |
| subject\_id  | INTEGER REFERENCES subjects(id) |                                                           |
| class\_id    | INTEGER REFERENCES classes(id)  |                                                           |
| title        | TEXT                            |                                                           |
| date         | DATE                            |                                                           |
| total\_score | INTEGER                         |                                                           |
| grade\_scale | TEXT                            | JSON (e.g., `{"A":76,"B":61,"C":51,"D":41,"E":31,"F":0}`) |

---

### 11. `test_results`

| Field       | Type                            | Notes |
| ----------- | ------------------------------- | ----- |
| id          | INTEGER PRIMARY KEY             |       |
| test\_id    | INTEGER REFERENCES tests(id)    |       |
| student\_id | INTEGER REFERENCES students(id) |       |
| score       | INTEGER                         |       |
| grade       | TEXT                            |       |

---

### 12. `student_reports`

| Field        | Type                            | Notes |
| ------------ | ------------------------------- | ----- |
| id           | INTEGER PRIMARY KEY             |       |
| student\_id  | INTEGER REFERENCES students(id) |       |
| subject\_id  | INTEGER REFERENCES subjects(id) |       |
| class\_id    | INTEGER REFERENCES classes(id)  |       |
| teacher\_id  | INTEGER REFERENCES teachers(id) |       |
| report\_text | TEXT                            |       |
| created\_at  | DATETIME                        |       |

---

## 🧠 Design Principles

* **Mobile-first** layout with Tailwind CSS
* **RESTful API** backend
* **Simple but secure authentication** (bcrypt)
* **Role-based UI rendering**
* **SQLite3** to simplify deployment for MVP

---

## 🔄 Future Enhancements (Not MVP)

* PDF report cards
* Bulk upload/import features
* Student messaging
* Semester-wise transcripts
* Notifications (email/SMS)
* Teacher-parent communication

---

Would you like me to generate:

* API endpoint plan?
* ER diagram for the schema?
* UI wireframe draft?
* A working Flask or FastAPI starter boilerplate using this plan?

Let me know how you'd like to proceed.
