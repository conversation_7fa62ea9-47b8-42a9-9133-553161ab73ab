import { sqliteTable, text, integer, blob, real } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table - handles authentication and basic user info
export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  fullName: text("full_name").notNull(),
  email: text("email").notNull().unique(),
  passwordHash: text("password_hash").notNull(),
  role: text("role").notNull().$type<"admin" | "teacher" | "parent">(),
  createdAt: integer("created_at", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
});

// Students table
export const students = sqliteTable("students", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  fullName: text("full_name").notNull(),
  dob: text("dob"), // SQLite doesn't have native date type, use text
  gender: text("gender"),
  registrationDate: integer("registration_date", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
  parentId: integer("parent_id").references(() => users.id),
  isActive: integer("is_active", { mode: "boolean" }).default(true).notNull(),
});

// Teachers table
export const teachers = sqliteTable("teachers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").references(() => users.id).notNull(),
  staffId: text("staff_id").notNull().unique(),
  hireDate: integer("hire_date", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
});

// Classes table
export const classes = sqliteTable("classes", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  academicYear: text("academic_year").notNull(),
});

// Subjects table
export const subjects = sqliteTable("subjects", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  description: text("description"),
  teacherId: integer("teacher_id").references(() => teachers.id),
});

// Class enrollments - many-to-many relationship between students and classes
export const classEnrollments = sqliteTable("class_enrollments", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  studentId: integer("student_id").references(() => students.id).notNull(),
  classId: integer("class_id").references(() => classes.id).notNull(),
  enrolledOn: integer("enrolled_on", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
});

// Subject classes - many-to-many relationship between subjects and classes
export const subjectClasses = sqliteTable("subject_classes", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  classId: integer("class_id").references(() => classes.id).notNull(),
  subjectId: integer("subject_id").references(() => subjects.id).notNull(),
});

// Attendance table
export const attendance = sqliteTable("attendance", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  studentId: integer("student_id").references(() => students.id).notNull(),
  subjectId: integer("subject_id").references(() => subjects.id).notNull(),
  classId: integer("class_id").references(() => classes.id).notNull(),
  date: text("date").notNull(), // SQLite doesn't have native date type, use text
  status: text("status").notNull().$type<"present" | "absent" | "excused">(),
});

// Materials table
export const materials = sqliteTable("materials", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  subjectId: integer("subject_id").references(() => subjects.id).notNull(),
  teacherId: integer("teacher_id").references(() => teachers.id).notNull(),
  title: text("title").notNull(),
  fileUrl: text("file_url"),
  notes: text("notes"),
  uploadedAt: integer("uploaded_at", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
});

// Tests table
export const tests = sqliteTable("tests", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  subjectId: integer("subject_id").references(() => subjects.id).notNull(),
  classId: integer("class_id").references(() => classes.id).notNull(),
  title: text("title").notNull(),
  date: text("date").notNull(), // SQLite doesn't have native date type, use text
  totalScore: integer("total_score").notNull(),
  gradeScale: text("grade_scale").notNull().$type<string>(), // Store JSON as text in SQLite
});

// Test results table
export const testResults = sqliteTable("test_results", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  testId: integer("test_id").references(() => tests.id).notNull(),
  studentId: integer("student_id").references(() => students.id).notNull(),
  score: integer("score").notNull(),
  grade: text("grade").notNull(),
});

// Student reports table
export const studentReports = sqliteTable("student_reports", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  studentId: integer("student_id").references(() => students.id).notNull(),
  subjectId: integer("subject_id").references(() => subjects.id).notNull(),
  classId: integer("class_id").references(() => classes.id).notNull(),
  teacherId: integer("teacher_id").references(() => teachers.id).notNull(),
  reportText: text("report_text").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).notNull().$defaultFn(() => new Date()),
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

export const insertStudentSchema = createInsertSchema(students).omit({
  id: true,
  registrationDate: true,
});

export const insertTeacherSchema = createInsertSchema(teachers).omit({
  id: true,
  hireDate: true,
});

export const insertClassSchema = createInsertSchema(classes).omit({
  id: true,
});

export const insertSubjectSchema = createInsertSchema(subjects).omit({
  id: true,
});

export const insertClassEnrollmentSchema = createInsertSchema(classEnrollments).omit({
  id: true,
  enrolledOn: true,
});

export const insertSubjectClassSchema = createInsertSchema(subjectClasses).omit({
  id: true,
});

export const insertAttendanceSchema = createInsertSchema(attendance).omit({
  id: true,
});

export const insertMaterialSchema = createInsertSchema(materials).omit({
  id: true,
  uploadedAt: true,
});

export const insertTestSchema = createInsertSchema(tests).omit({
  id: true,
});

export const insertTestResultSchema = createInsertSchema(testResults).omit({
  id: true,
});

export const insertStudentReportSchema = createInsertSchema(studentReports).omit({
  id: true,
  createdAt: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Student = typeof students.$inferSelect;
export type InsertStudent = z.infer<typeof insertStudentSchema>;
export type Teacher = typeof teachers.$inferSelect;
export type InsertTeacher = z.infer<typeof insertTeacherSchema>;
export type Class = typeof classes.$inferSelect;
export type InsertClass = z.infer<typeof insertClassSchema>;
export type Subject = typeof subjects.$inferSelect;
export type InsertSubject = z.infer<typeof insertSubjectSchema>;
export type ClassEnrollment = typeof classEnrollments.$inferSelect;
export type InsertClassEnrollment = z.infer<typeof insertClassEnrollmentSchema>;
export type SubjectClass = typeof subjectClasses.$inferSelect;
export type InsertSubjectClass = z.infer<typeof insertSubjectClassSchema>;
export type Attendance = typeof attendance.$inferSelect;
export type InsertAttendance = z.infer<typeof insertAttendanceSchema>;
export type Material = typeof materials.$inferSelect;
export type InsertMaterial = z.infer<typeof insertMaterialSchema>;
export type Test = typeof tests.$inferSelect;
export type InsertTest = z.infer<typeof insertTestSchema>;
export type TestResult = typeof testResults.$inferSelect;
export type InsertTestResult = z.infer<typeof insertTestResultSchema>;
export type StudentReport = typeof studentReports.$inferSelect;
export type InsertStudentReport = z.infer<typeof insertStudentReportSchema>;

// Login schema
export const loginSchema = z.object({
  email: z.string().min(1, "Email is required"),
  password: z.string().min(1, "Password is required"),
});

export type LoginRequest = z.infer<typeof loginSchema>;

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  students: many(students),
  teachers: many(teachers),
}));

export const studentsRelations = relations(students, ({ one, many }) => ({
  parent: one(users, {
    fields: [students.parentId],
    references: [users.id],
  }),
  classEnrollments: many(classEnrollments),
  attendance: many(attendance),
  testResults: many(testResults),
  reports: many(studentReports),
}));

export const teachersRelations = relations(teachers, ({ one, many }) => ({
  user: one(users, {
    fields: [teachers.userId],
    references: [users.id],
  }),
  subjects: many(subjects),
  materials: many(materials),
  reports: many(studentReports),
}));

export const classesRelations = relations(classes, ({ many }) => ({
  enrollments: many(classEnrollments),
  subjectClasses: many(subjectClasses),
  tests: many(tests),
  attendance: many(attendance),
  reports: many(studentReports),
}));

export const subjectsRelations = relations(subjects, ({ one, many }) => ({
  teacher: one(teachers, {
    fields: [subjects.teacherId],
    references: [teachers.id],
  }),
  subjectClasses: many(subjectClasses),
  materials: many(materials),
  tests: many(tests),
  attendance: many(attendance),
  reports: many(studentReports),
}));

export const classEnrollmentsRelations = relations(classEnrollments, ({ one }) => ({
  student: one(students, {
    fields: [classEnrollments.studentId],
    references: [students.id],
  }),
  class: one(classes, {
    fields: [classEnrollments.classId],
    references: [classes.id],
  }),
}));

export const subjectClassesRelations = relations(subjectClasses, ({ one }) => ({
  class: one(classes, {
    fields: [subjectClasses.classId],
    references: [classes.id],
  }),
  subject: one(subjects, {
    fields: [subjectClasses.subjectId],
    references: [subjects.id],
  }),
}));

export const attendanceRelations = relations(attendance, ({ one }) => ({
  student: one(students, {
    fields: [attendance.studentId],
    references: [students.id],
  }),
  subject: one(subjects, {
    fields: [attendance.subjectId],
    references: [subjects.id],
  }),
  class: one(classes, {
    fields: [attendance.classId],
    references: [classes.id],
  }),
}));

export const materialsRelations = relations(materials, ({ one }) => ({
  subject: one(subjects, {
    fields: [materials.subjectId],
    references: [subjects.id],
  }),
  teacher: one(teachers, {
    fields: [materials.teacherId],
    references: [teachers.id],
  }),
}));

export const testsRelations = relations(tests, ({ one, many }) => ({
  subject: one(subjects, {
    fields: [tests.subjectId],
    references: [subjects.id],
  }),
  class: one(classes, {
    fields: [tests.classId],
    references: [classes.id],
  }),
  results: many(testResults),
}));

export const testResultsRelations = relations(testResults, ({ one }) => ({
  test: one(tests, {
    fields: [testResults.testId],
    references: [tests.id],
  }),
  student: one(students, {
    fields: [testResults.studentId],
    references: [students.id],
  }),
}));

export const studentReportsRelations = relations(studentReports, ({ one }) => ({
  student: one(students, {
    fields: [studentReports.studentId],
    references: [students.id],
  }),
  subject: one(subjects, {
    fields: [studentReports.subjectId],
    references: [subjects.id],
  }),
  class: one(classes, {
    fields: [studentReports.classId],
    references: [classes.id],
  }),
  teacher: one(teachers, {
    fields: [studentReports.teacherId],
    references: [teachers.id],
  }),
}));
