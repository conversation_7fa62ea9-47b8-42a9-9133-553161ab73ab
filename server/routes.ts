import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import bcrypt from "bcrypt";
import session from "express-session";
import { storage } from "./storage";
import { loginSchema, insertStudentSchema, insertClassSchema, insertSubjectSchema, insertTestSchema, insertAttendanceSchema } from "@shared/schema";

declare module "express-session" {
  interface SessionData {
    userId?: number;
    userRole?: string;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Session middleware
  app.use(session({
    secret: process.env.SESSION_SECRET || "dprincej-college-secret",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // Set to true in production with HTTPS
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
  }));

  // Authentication middleware
  const requireAuth = (req: Request, res: Response, next: Function) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Authentication required" });
    }
    next();
  };

  const requireRole = (roles: string[]) => {
    return (req: Request, res: Response, next: Function) => {
      if (!req.session.userRole || !roles.includes(req.session.userRole)) {
        return res.status(403).json({ message: "Insufficient permissions" });
      }
      next();
    };
  };

  // Authentication routes
  app.post("/api/login", async (req: Request, res: Response) => {
    try {
      const { email, password } = loginSchema.parse(req.body);
      
      const user = await storage.getUserByEmail(email);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      const validPassword = await bcrypt.compare(password, user.passwordHash);
      if (!validPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      req.session.userId = user.id;
      req.session.userRole = user.role;

      res.json({
        user: {
          id: user.id,
          fullName: user.fullName,
          email: user.email,
          role: user.role
        }
      });
    } catch (error) {
      res.status(400).json({ message: "Invalid request data" });
    }
  });

  app.post("/api/logout", (req: Request, res: Response) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      res.json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/me", requireAuth, async (req: Request, res: Response) => {
    // Find user by ID instead of email
    const users = await storage.getUsers();
    const user = users.find(u => u.id === req.session.userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.json({
      user: {
        id: user.id,
        fullName: user.fullName,
        email: user.email,
        role: user.role
      }
    });
  });

  // Student routes
  app.get("/api/students", requireAuth, async (req: Request, res: Response) => {
    try {
      const students = await storage.getStudents();
      res.json(students);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch students" });
    }
  });

  app.get("/api/students/parent/:parentId", requireAuth, async (req: Request, res: Response) => {
    try {
      const parentId = parseInt(req.params.parentId);
      const students = await storage.getStudentsByParentId(parentId);
      res.json(students);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch students" });
    }
  });

  app.post("/api/students", requireAuth, requireRole(["admin", "teacher"]), async (req: Request, res: Response) => {
    try {
      const studentData = insertStudentSchema.parse(req.body);
      const student = await storage.createStudent(studentData);
      res.status(201).json(student);
    } catch (error) {
      res.status(400).json({ message: "Invalid student data" });
    }
  });

  app.put("/api/students/:id", requireAuth, requireRole(["admin", "teacher"]), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const updateData = insertStudentSchema.partial().parse(req.body);
      const student = await storage.updateStudent(id, updateData);
      
      if (!student) {
        return res.status(404).json({ message: "Student not found" });
      }
      
      res.json(student);
    } catch (error) {
      res.status(400).json({ message: "Invalid student data" });
    }
  });

  // Class routes
  app.get("/api/classes", requireAuth, async (req: Request, res: Response) => {
    try {
      const classes = await storage.getClasses();
      res.json(classes);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch classes" });
    }
  });

  app.post("/api/classes", requireAuth, requireRole(["admin"]), async (req: Request, res: Response) => {
    try {
      const classData = insertClassSchema.parse(req.body);
      const newClass = await storage.createClass(classData);
      res.status(201).json(newClass);
    } catch (error) {
      res.status(400).json({ message: "Invalid class data" });
    }
  });

  // Subject routes
  app.get("/api/subjects", requireAuth, async (req: Request, res: Response) => {
    try {
      const subjects = await storage.getSubjects();
      res.json(subjects);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch subjects" });
    }
  });

  app.get("/api/subjects/teacher/:teacherId", requireAuth, async (req: Request, res: Response) => {
    try {
      const teacherId = parseInt(req.params.teacherId);
      const subjects = await storage.getSubjectsByTeacherId(teacherId);
      res.json(subjects);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch subjects" });
    }
  });

  app.post("/api/subjects", requireAuth, requireRole(["admin"]), async (req: Request, res: Response) => {
    try {
      const subjectData = insertSubjectSchema.parse(req.body);
      const subject = await storage.createSubject(subjectData);
      res.status(201).json(subject);
    } catch (error) {
      res.status(400).json({ message: "Invalid subject data" });
    }
  });

  // Test routes
  app.get("/api/tests", requireAuth, async (req: Request, res: Response) => {
    try {
      const tests = await storage.getTests();
      res.json(tests);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tests" });
    }
  });

  app.get("/api/tests/subject/:subjectId", requireAuth, async (req: Request, res: Response) => {
    try {
      const subjectId = parseInt(req.params.subjectId);
      const tests = await storage.getTestsBySubjectId(subjectId);
      res.json(tests);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tests" });
    }
  });

  app.post("/api/tests", requireAuth, requireRole(["admin", "teacher"]), async (req: Request, res: Response) => {
    try {
      const testData = insertTestSchema.parse(req.body);
      const test = await storage.createTest(testData);
      res.status(201).json(test);
    } catch (error) {
      res.status(400).json({ message: "Invalid test data" });
    }
  });

  // Attendance routes
  app.get("/api/attendance", requireAuth, async (req: Request, res: Response) => {
    try {
      const { date, studentId } = req.query;
      
      let attendance;
      if (date) {
        attendance = await storage.getAttendanceByDate(date as string);
      } else if (studentId) {
        attendance = await storage.getAttendanceByStudentId(parseInt(studentId as string));
      } else {
        attendance = await storage.getAttendance();
      }
      
      res.json(attendance);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch attendance" });
    }
  });

  app.post("/api/attendance", requireAuth, requireRole(["admin", "teacher"]), async (req: Request, res: Response) => {
    try {
      const attendanceData = insertAttendanceSchema.parse(req.body);
      const attendance = await storage.createAttendance(attendanceData);
      res.status(201).json(attendance);
    } catch (error) {
      res.status(400).json({ message: "Invalid attendance data" });
    }
  });

  // Test results routes
  app.get("/api/test-results/test/:testId", requireAuth, async (req: Request, res: Response) => {
    try {
      const testId = parseInt(req.params.testId);
      const results = await storage.getTestResultsByTestId(testId);
      res.json(results);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch test results" });
    }
  });

  app.get("/api/test-results/student/:studentId", requireAuth, async (req: Request, res: Response) => {
    try {
      const studentId = parseInt(req.params.studentId);
      const results = await storage.getTestResultsByStudentId(studentId);
      res.json(results);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch test results" });
    }
  });

  // Statistics routes for dashboard
  app.get("/api/stats", requireAuth, async (req: Request, res: Response) => {
    try {
      const students = await storage.getStudents();
      const classes = await storage.getClasses();
      const tests = await storage.getTests();
      const todayAttendance = await storage.getAttendanceByDate(new Date().toISOString().split('T')[0]);
      
      const stats = {
        totalStudents: students.length,
        activeClasses: classes.length,
        pendingTests: tests.length,
        presentToday: todayAttendance.filter(att => att.status === 'present').length
      };
      
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch statistics" });
    }
  });

  // Teacher routes
  app.get("/api/teachers", requireAuth, async (req: Request, res: Response) => {
    try {
      const teachers = await storage.getTeachers();
      res.json(teachers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch teachers" });
    }
  });

  app.get("/api/teachers/user/:userId", requireAuth, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const teacher = await storage.getTeacherByUserId(userId);
      res.json(teacher);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch teacher" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
