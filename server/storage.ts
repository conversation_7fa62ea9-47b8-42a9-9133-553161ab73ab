import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import { db } from "./db";
import {
  users, students, teachers, classes, subjects, classEnrollments, subjectClasses,
  attendance, materials, tests, testResults, studentReports,
  type User, type InsertUser, type Student, type InsertStudent,
  type Teacher, type InsertTeacher, type Class, type InsertClass,
  type Subject, type InsertSubject, type ClassEnrollment, type InsertClassEnrollment,
  type SubjectClass, type InsertSubjectClass, type Attendance, type InsertAttendance,
  type Material, type InsertMaterial, type Test, type InsertTest,
  type TestResult, type InsertTestResult, type StudentReport, type InsertStudentReport
} from "@shared/schema";

export interface IStorage {
  // User operations
  getUsers(): Promise<User[]>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Student operations
  getStudents(): Promise<Student[]>;
  getStudentById(id: number): Promise<Student | undefined>;
  getStudentsByParentId(parentId: number): Promise<Student[]>;
  createStudent(student: InsertStudent): Promise<Student>;
  updateStudent(id: number, student: Partial<InsertStudent>): Promise<Student | undefined>;
  
  // Teacher operations
  getTeachers(): Promise<Teacher[]>;
  getTeacherById(id: number): Promise<Teacher | undefined>;
  getTeacherByUserId(userId: number): Promise<Teacher | undefined>;
  createTeacher(teacher: InsertTeacher): Promise<Teacher>;
  
  // Class operations
  getClasses(): Promise<Class[]>;
  getClassById(id: number): Promise<Class | undefined>;
  createClass(classData: InsertClass): Promise<Class>;
  updateClass(id: number, classData: Partial<InsertClass>): Promise<Class | undefined>;
  
  // Subject operations
  getSubjects(): Promise<Subject[]>;
  getSubjectById(id: number): Promise<Subject | undefined>;
  getSubjectsByTeacherId(teacherId: number): Promise<Subject[]>;
  createSubject(subject: InsertSubject): Promise<Subject>;
  
  // Class enrollment operations
  getClassEnrollments(): Promise<ClassEnrollment[]>;
  getEnrollmentsByStudentId(studentId: number): Promise<ClassEnrollment[]>;
  getEnrollmentsByClassId(classId: number): Promise<ClassEnrollment[]>;
  createClassEnrollment(enrollment: InsertClassEnrollment): Promise<ClassEnrollment>;
  
  // Subject class operations
  getSubjectClasses(): Promise<SubjectClass[]>;
  getSubjectsByClassId(classId: number): Promise<Subject[]>;
  getClassesBySubjectId(subjectId: number): Promise<Class[]>;
  createSubjectClass(subjectClass: InsertSubjectClass): Promise<SubjectClass>;
  
  // Attendance operations
  getAttendance(): Promise<Attendance[]>;
  getAttendanceByDate(date: string): Promise<Attendance[]>;
  getAttendanceByStudentId(studentId: number): Promise<Attendance[]>;
  createAttendance(attendance: InsertAttendance): Promise<Attendance>;
  
  // Material operations
  getMaterials(): Promise<Material[]>;
  getMaterialsBySubjectId(subjectId: number): Promise<Material[]>;
  createMaterial(material: InsertMaterial): Promise<Material>;
  
  // Test operations
  getTests(): Promise<Test[]>;
  getTestById(id: number): Promise<Test | undefined>;
  getTestsBySubjectId(subjectId: number): Promise<Test[]>;
  createTest(test: InsertTest): Promise<Test>;
  
  // Test result operations
  getTestResults(): Promise<TestResult[]>;
  getTestResultsByTestId(testId: number): Promise<TestResult[]>;
  getTestResultsByStudentId(studentId: number): Promise<TestResult[]>;
  createTestResult(testResult: InsertTestResult): Promise<TestResult>;
  
  // Student report operations
  getStudentReports(): Promise<StudentReport[]>;
  getReportsByStudentId(studentId: number): Promise<StudentReport[]>;
  createStudentReport(report: InsertStudentReport): Promise<StudentReport>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private students: Map<number, Student> = new Map();
  private teachers: Map<number, Teacher> = new Map();
  private classes: Map<number, Class> = new Map();
  private subjects: Map<number, Subject> = new Map();
  private classEnrollments: Map<number, ClassEnrollment> = new Map();
  private subjectClasses: Map<number, SubjectClass> = new Map();
  private attendance: Map<number, Attendance> = new Map();
  private materials: Map<number, Material> = new Map();
  private tests: Map<number, Test> = new Map();
  private testResults: Map<number, TestResult> = new Map();
  private studentReports: Map<number, StudentReport> = new Map();
  
  private currentId = 1;

  constructor() {
    this.seedData();
  }

  private async seedData() {
    // Create demo users with bcrypt hashed passwords
    const parentUser = await this.createUser({
      fullName: "Mr. Adebayo Kunle",
      email: "daddy",
      passwordHash: await bcrypt.hash("mummy", 10),
      role: "parent"
    });

    const teacherUser = await this.createUser({
      fullName: "Mrs. Sarah Johnson",
      email: "headmaster",
      passwordHash: await bcrypt.hash("jss6334", 10),
      role: "teacher"
    });

    const adminUser = await this.createUser({
      fullName: "Dr. Michael Brown",
      email: "administrator",
      passwordHash: await bcrypt.hash("okokomaiko", 10),
      role: "admin"
    });

    // Create teacher record
    const teacher = await this.createTeacher({
      userId: teacherUser.id,
      staffId: "TCH001"
    });

    // Create classes
    const class9A = await this.createClass({
      name: "Grade 9-A",
      academicYear: "2023/2024"
    });

    const class8B = await this.createClass({
      name: "Grade 8-B",
      academicYear: "2023/2024"
    });

    // Create subjects
    const mathSubject = await this.createSubject({
      name: "Mathematics",
      description: "Advanced Mathematics",
      teacherId: teacher.id
    });

    const englishSubject = await this.createSubject({
      name: "English Language",
      description: "English Language and Literature",
      teacherId: teacher.id
    });

    // Create students
    const student1 = await this.createStudent({
      fullName: "Adebayo Benjamin",
      dob: "2008-05-15",
      gender: "Male",
      parentId: parentUser.id,
      isActive: true
    });

    const student2 = await this.createStudent({
      fullName: "Fatima Okafor",
      dob: "2009-03-22",
      gender: "Female",
      parentId: parentUser.id,
      isActive: true
    });

    // Enroll students in classes
    await this.createClassEnrollment({
      studentId: student1.id,
      classId: class9A.id
    });

    await this.createClassEnrollment({
      studentId: student2.id,
      classId: class8B.id
    });

    // Assign subjects to classes
    await this.createSubjectClass({
      classId: class9A.id,
      subjectId: mathSubject.id
    });

    await this.createSubjectClass({
      classId: class8B.id,
      subjectId: englishSubject.id
    });

    // Create tests
    const mathTest = await this.createTest({
      subjectId: mathSubject.id,
      classId: class9A.id,
      title: "Mid-term Mathematics Test",
      date: "2024-01-10",
      totalScore: 100,
      gradeScale: JSON.stringify({ A: 80, B: 70, C: 60, D: 50, F: 0 })
    });

    // Create test results
    await this.createTestResult({
      testId: mathTest.id,
      studentId: student1.id,
      score: 85,
      grade: "A"
    });

    // Create more students for better demo
    const student3 = await this.createStudent({
      fullName: "Chioma Nwosu",
      dob: "2008-08-12",
      gender: "Female",
      parentId: parentUser.id,
      isActive: true
    });

    const student4 = await this.createStudent({
      fullName: "Ibrahim Musa",
      dob: "2009-01-28",
      gender: "Male",
      parentId: parentUser.id,
      isActive: true
    });

    const student5 = await this.createStudent({
      fullName: "Grace Okoro",
      dob: "2008-11-05",
      gender: "Female",
      parentId: parentUser.id,
      isActive: false
    });

    // Enroll additional students
    await this.createClassEnrollment({
      studentId: student3.id,
      classId: class9A.id
    });

    await this.createClassEnrollment({
      studentId: student4.id,
      classId: class8B.id
    });

    await this.createClassEnrollment({
      studentId: student5.id,
      classId: class9A.id
    });

    // Assign more subjects to classes
    await this.createSubjectClass({
      classId: class9A.id,
      subjectId: englishSubject.id
    });

    await this.createSubjectClass({
      classId: class8B.id,
      subjectId: mathSubject.id
    });

    // Create more tests
    const englishTest = await this.createTest({
      subjectId: englishSubject.id,
      classId: class9A.id,
      title: "Essay Writing Assessment",
      date: "2024-01-20",
      totalScore: 50,
      gradeScale: JSON.stringify({ A: 40, B: 35, C: 30, D: 25, F: 0 })
    });

    // Create test results for multiple students
    await this.createTestResult({
      testId: mathTest.id,
      studentId: student3.id,
      score: 72,
      grade: "B"
    });

    await this.createTestResult({
      testId: englishTest.id,
      studentId: student1.id,
      score: 42,
      grade: "A"
    });

    await this.createTestResult({
      testId: englishTest.id,
      studentId: student3.id,
      score: 38,
      grade: "B"
    });

    // Create attendance records for multiple students and dates
    const attendanceDates = ["2024-01-15", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19"];
    const students = [student1, student2, student3, student4];
    
    for (const date of attendanceDates) {
      for (const student of students) {
        const status = Math.random() > 0.1 ? "present" : (Math.random() > 0.5 ? "absent" : "excused");
        await this.createAttendance({
          studentId: student.id,
          subjectId: mathSubject.id,
          classId: student.id === student2.id || student.id === student4.id ? class8B.id : class9A.id,
          date: date,
          status: status as "present" | "absent" | "excused"
        });
      }
    }

    // Create materials
    await this.createMaterial({
      subjectId: mathSubject.id,
      teacherId: teacher.id,
      title: "Algebra Fundamentals",
      notes: "Introduction to algebraic expressions and equations",
      fileUrl: null
    });

    await this.createMaterial({
      subjectId: englishSubject.id,
      teacherId: teacher.id,
      title: "Essay Writing Guidelines",
      notes: "Complete guide to writing structured essays",
      fileUrl: null
    });

    // Create student reports
    await this.createStudentReport({
      studentId: student1.id,
      subjectId: mathSubject.id,
      classId: class9A.id,
      teacherId: teacher.id,
      reportText: "Adebayo shows excellent understanding of mathematical concepts and consistently produces high-quality work. He actively participates in class discussions and helps other students."
    });

    await this.createStudentReport({
      studentId: student1.id,
      subjectId: englishSubject.id,
      classId: class9A.id,
      teacherId: teacher.id,
      reportText: "Good progress in reading comprehension and writing skills. Adebayo should focus on expanding his vocabulary and practicing essay writing structure."
    });
  }

  // User operations
  async getUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = {
      ...insertUser,
      id,
      createdAt: new Date(),
      role: insertUser.role as "admin" | "teacher" | "parent"
    };
    this.users.set(id, user);
    return user;
  }

  // Student operations
  async getStudents(): Promise<Student[]> {
    return Array.from(this.students.values());
  }

  async getStudentById(id: number): Promise<Student | undefined> {
    return this.students.get(id);
  }

  async getStudentsByParentId(parentId: number): Promise<Student[]> {
    return Array.from(this.students.values()).filter(student => student.parentId === parentId);
  }

  async createStudent(insertStudent: InsertStudent): Promise<Student> {
    const id = this.currentId++;
    const student: Student = {
      id,
      fullName: insertStudent.fullName,
      dob: insertStudent.dob || null,
      gender: insertStudent.gender || null,
      registrationDate: new Date(),
      parentId: insertStudent.parentId || null,
      isActive: insertStudent.isActive ?? true
    };
    this.students.set(id, student);
    return student;
  }

  async updateStudent(id: number, updateData: Partial<InsertStudent>): Promise<Student | undefined> {
    const existing = this.students.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...updateData };
    this.students.set(id, updated);
    return updated;
  }

  // Teacher operations
  async getTeachers(): Promise<Teacher[]> {
    return Array.from(this.teachers.values());
  }

  async getTeacherById(id: number): Promise<Teacher | undefined> {
    return this.teachers.get(id);
  }

  async getTeacherByUserId(userId: number): Promise<Teacher | undefined> {
    return Array.from(this.teachers.values()).find(teacher => teacher.userId === userId);
  }

  async createTeacher(insertTeacher: InsertTeacher): Promise<Teacher> {
    const id = this.currentId++;
    const teacher: Teacher = {
      ...insertTeacher,
      id,
      hireDate: new Date()
    };
    this.teachers.set(id, teacher);
    return teacher;
  }

  // Class operations
  async getClasses(): Promise<Class[]> {
    return Array.from(this.classes.values());
  }

  async getClassById(id: number): Promise<Class | undefined> {
    return this.classes.get(id);
  }

  async createClass(insertClass: InsertClass): Promise<Class> {
    const id = this.currentId++;
    const classData: Class = {
      ...insertClass,
      id
    };
    this.classes.set(id, classData);
    return classData;
  }

  async updateClass(id: number, updateData: Partial<InsertClass>): Promise<Class | undefined> {
    const existing = this.classes.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...updateData };
    this.classes.set(id, updated);
    return updated;
  }

  // Subject operations
  async getSubjects(): Promise<Subject[]> {
    return Array.from(this.subjects.values());
  }

  async getSubjectById(id: number): Promise<Subject | undefined> {
    return this.subjects.get(id);
  }

  async getSubjectsByTeacherId(teacherId: number): Promise<Subject[]> {
    return Array.from(this.subjects.values()).filter(subject => subject.teacherId === teacherId);
  }

  async createSubject(insertSubject: InsertSubject): Promise<Subject> {
    const id = this.currentId++;
    const subject: Subject = {
      id,
      name: insertSubject.name,
      description: insertSubject.description || null,
      teacherId: insertSubject.teacherId || null
    };
    this.subjects.set(id, subject);
    return subject;
  }

  // Class enrollment operations
  async getClassEnrollments(): Promise<ClassEnrollment[]> {
    return Array.from(this.classEnrollments.values());
  }

  async getEnrollmentsByStudentId(studentId: number): Promise<ClassEnrollment[]> {
    return Array.from(this.classEnrollments.values()).filter(enrollment => enrollment.studentId === studentId);
  }

  async getEnrollmentsByClassId(classId: number): Promise<ClassEnrollment[]> {
    return Array.from(this.classEnrollments.values()).filter(enrollment => enrollment.classId === classId);
  }

  async createClassEnrollment(insertEnrollment: InsertClassEnrollment): Promise<ClassEnrollment> {
    const id = this.currentId++;
    const enrollment: ClassEnrollment = {
      ...insertEnrollment,
      id,
      enrolledOn: new Date()
    };
    this.classEnrollments.set(id, enrollment);
    return enrollment;
  }

  // Subject class operations
  async getSubjectClasses(): Promise<SubjectClass[]> {
    return Array.from(this.subjectClasses.values());
  }

  async getSubjectsByClassId(classId: number): Promise<Subject[]> {
    const subjectClassLinks = Array.from(this.subjectClasses.values()).filter(sc => sc.classId === classId);
    return subjectClassLinks.map(link => this.subjects.get(link.subjectId)).filter(Boolean) as Subject[];
  }

  async getClassesBySubjectId(subjectId: number): Promise<Class[]> {
    const subjectClassLinks = Array.from(this.subjectClasses.values()).filter(sc => sc.subjectId === subjectId);
    return subjectClassLinks.map(link => this.classes.get(link.classId)).filter(Boolean) as Class[];
  }

  async createSubjectClass(insertSubjectClass: InsertSubjectClass): Promise<SubjectClass> {
    const id = this.currentId++;
    const subjectClass: SubjectClass = {
      ...insertSubjectClass,
      id
    };
    this.subjectClasses.set(id, subjectClass);
    return subjectClass;
  }

  // Attendance operations
  async getAttendance(): Promise<Attendance[]> {
    return Array.from(this.attendance.values());
  }

  async getAttendanceByDate(date: string): Promise<Attendance[]> {
    return Array.from(this.attendance.values()).filter(att => att.date === date);
  }

  async getAttendanceByStudentId(studentId: number): Promise<Attendance[]> {
    return Array.from(this.attendance.values()).filter(att => att.studentId === studentId);
  }

  async createAttendance(insertAttendance: InsertAttendance): Promise<Attendance> {
    const id = this.currentId++;
    const attendance: Attendance = {
      id,
      studentId: insertAttendance.studentId,
      subjectId: insertAttendance.subjectId,
      classId: insertAttendance.classId,
      date: insertAttendance.date,
      status: insertAttendance.status as "present" | "absent" | "excused"
    };
    this.attendance.set(id, attendance);
    return attendance;
  }

  // Material operations
  async getMaterials(): Promise<Material[]> {
    return Array.from(this.materials.values());
  }

  async getMaterialsBySubjectId(subjectId: number): Promise<Material[]> {
    return Array.from(this.materials.values()).filter(material => material.subjectId === subjectId);
  }

  async createMaterial(insertMaterial: InsertMaterial): Promise<Material> {
    const id = this.currentId++;
    const material: Material = {
      id,
      subjectId: insertMaterial.subjectId,
      teacherId: insertMaterial.teacherId,
      title: insertMaterial.title,
      fileUrl: insertMaterial.fileUrl || null,
      notes: insertMaterial.notes || null,
      uploadedAt: new Date()
    };
    this.materials.set(id, material);
    return material;
  }

  // Test operations
  async getTests(): Promise<Test[]> {
    return Array.from(this.tests.values());
  }

  async getTestById(id: number): Promise<Test | undefined> {
    return this.tests.get(id);
  }

  async getTestsBySubjectId(subjectId: number): Promise<Test[]> {
    return Array.from(this.tests.values()).filter(test => test.subjectId === subjectId);
  }

  async createTest(insertTest: InsertTest): Promise<Test> {
    const id = this.currentId++;
    const test: Test = {
      ...insertTest,
      id
    };
    this.tests.set(id, test);
    return test;
  }

  // Test result operations
  async getTestResults(): Promise<TestResult[]> {
    return Array.from(this.testResults.values());
  }

  async getTestResultsByTestId(testId: number): Promise<TestResult[]> {
    return Array.from(this.testResults.values()).filter(result => result.testId === testId);
  }

  async getTestResultsByStudentId(studentId: number): Promise<TestResult[]> {
    return Array.from(this.testResults.values()).filter(result => result.studentId === studentId);
  }

  async createTestResult(insertTestResult: InsertTestResult): Promise<TestResult> {
    const id = this.currentId++;
    const testResult: TestResult = {
      ...insertTestResult,
      id
    };
    this.testResults.set(id, testResult);
    return testResult;
  }

  // Student report operations
  async getStudentReports(): Promise<StudentReport[]> {
    return Array.from(this.studentReports.values());
  }

  async getReportsByStudentId(studentId: number): Promise<StudentReport[]> {
    return Array.from(this.studentReports.values()).filter(report => report.studentId === studentId);
  }

  async createStudentReport(insertReport: InsertStudentReport): Promise<StudentReport> {
    const id = this.currentId++;
    const report: StudentReport = {
      ...insertReport,
      id,
      createdAt: new Date()
    };
    this.studentReports.set(id, report);
    return report;
  }
}

export class DatabaseStorage implements IStorage {
  async getUsers(): Promise<User[]> {
    return await db.select().from(users);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getStudents(): Promise<Student[]> {
    return await db.select().from(students);
  }

  async getStudentById(id: number): Promise<Student | undefined> {
    const [student] = await db.select().from(students).where(eq(students.id, id));
    return student || undefined;
  }

  async getStudentsByParentId(parentId: number): Promise<Student[]> {
    return await db.select().from(students).where(eq(students.parentId, parentId));
  }

  async createStudent(insertStudent: InsertStudent): Promise<Student> {
    const [student] = await db
      .insert(students)
      .values(insertStudent)
      .returning();
    return student;
  }

  async updateStudent(id: number, updateData: Partial<InsertStudent>): Promise<Student | undefined> {
    const [student] = await db
      .update(students)
      .set(updateData)
      .where(eq(students.id, id))
      .returning();
    return student || undefined;
  }

  async getTeachers(): Promise<Teacher[]> {
    return await db.select().from(teachers);
  }

  async getTeacherById(id: number): Promise<Teacher | undefined> {
    const [teacher] = await db.select().from(teachers).where(eq(teachers.id, id));
    return teacher || undefined;
  }

  async getTeacherByUserId(userId: number): Promise<Teacher | undefined> {
    const [teacher] = await db.select().from(teachers).where(eq(teachers.userId, userId));
    return teacher || undefined;
  }

  async createTeacher(insertTeacher: InsertTeacher): Promise<Teacher> {
    const [teacher] = await db
      .insert(teachers)
      .values(insertTeacher)
      .returning();
    return teacher;
  }

  async getClasses(): Promise<Class[]> {
    return await db.select().from(classes);
  }

  async getClassById(id: number): Promise<Class | undefined> {
    const [classItem] = await db.select().from(classes).where(eq(classes.id, id));
    return classItem || undefined;
  }

  async createClass(insertClass: InsertClass): Promise<Class> {
    const [classItem] = await db
      .insert(classes)
      .values(insertClass)
      .returning();
    return classItem;
  }

  async updateClass(id: number, updateData: Partial<InsertClass>): Promise<Class | undefined> {
    const [classItem] = await db
      .update(classes)
      .set(updateData)
      .where(eq(classes.id, id))
      .returning();
    return classItem || undefined;
  }

  async getSubjects(): Promise<Subject[]> {
    return await db.select().from(subjects);
  }

  async getSubjectById(id: number): Promise<Subject | undefined> {
    const [subject] = await db.select().from(subjects).where(eq(subjects.id, id));
    return subject || undefined;
  }

  async getSubjectsByTeacherId(teacherId: number): Promise<Subject[]> {
    return await db.select().from(subjects).where(eq(subjects.teacherId, teacherId));
  }

  async createSubject(insertSubject: InsertSubject): Promise<Subject> {
    const [subject] = await db
      .insert(subjects)
      .values(insertSubject)
      .returning();
    return subject;
  }

  async getClassEnrollments(): Promise<ClassEnrollment[]> {
    return await db.select().from(classEnrollments);
  }

  async getEnrollmentsByStudentId(studentId: number): Promise<ClassEnrollment[]> {
    return await db.select().from(classEnrollments).where(eq(classEnrollments.studentId, studentId));
  }

  async getEnrollmentsByClassId(classId: number): Promise<ClassEnrollment[]> {
    return await db.select().from(classEnrollments).where(eq(classEnrollments.classId, classId));
  }

  async createClassEnrollment(insertEnrollment: InsertClassEnrollment): Promise<ClassEnrollment> {
    const [enrollment] = await db
      .insert(classEnrollments)
      .values(insertEnrollment)
      .returning();
    return enrollment;
  }

  async getSubjectClasses(): Promise<SubjectClass[]> {
    return await db.select().from(subjectClasses);
  }

  async getSubjectsByClassId(classId: number): Promise<Subject[]> {
    const subjectClassLinks = await db.select().from(subjectClasses).where(eq(subjectClasses.classId, classId));
    const subjectIds = subjectClassLinks.map(link => link.subjectId);
    if (subjectIds.length === 0) return [];
    
    return await db.select().from(subjects).where(eq(subjects.id, subjectIds[0])); // Simplified for now
  }

  async getClassesBySubjectId(subjectId: number): Promise<Class[]> {
    const subjectClassLinks = await db.select().from(subjectClasses).where(eq(subjectClasses.subjectId, subjectId));
    const classIds = subjectClassLinks.map(link => link.classId);
    if (classIds.length === 0) return [];
    
    return await db.select().from(classes).where(eq(classes.id, classIds[0])); // Simplified for now
  }

  async createSubjectClass(insertSubjectClass: InsertSubjectClass): Promise<SubjectClass> {
    const [subjectClass] = await db
      .insert(subjectClasses)
      .values(insertSubjectClass)
      .returning();
    return subjectClass;
  }

  async getAttendance(): Promise<Attendance[]> {
    return await db.select().from(attendance);
  }

  async getAttendanceByDate(date: string): Promise<Attendance[]> {
    return await db.select().from(attendance).where(eq(attendance.date, date));
  }

  async getAttendanceByStudentId(studentId: number): Promise<Attendance[]> {
    return await db.select().from(attendance).where(eq(attendance.studentId, studentId));
  }

  async createAttendance(insertAttendance: InsertAttendance): Promise<Attendance> {
    const [attendanceRecord] = await db
      .insert(attendance)
      .values(insertAttendance)
      .returning();
    return attendanceRecord;
  }

  async getMaterials(): Promise<Material[]> {
    return await db.select().from(materials);
  }

  async getMaterialsBySubjectId(subjectId: number): Promise<Material[]> {
    return await db.select().from(materials).where(eq(materials.subjectId, subjectId));
  }

  async createMaterial(insertMaterial: InsertMaterial): Promise<Material> {
    const [material] = await db
      .insert(materials)
      .values(insertMaterial)
      .returning();
    return material;
  }

  async getTests(): Promise<Test[]> {
    return await db.select().from(tests);
  }

  async getTestById(id: number): Promise<Test | undefined> {
    const [test] = await db.select().from(tests).where(eq(tests.id, id));
    return test || undefined;
  }

  async getTestsBySubjectId(subjectId: number): Promise<Test[]> {
    return await db.select().from(tests).where(eq(tests.subjectId, subjectId));
  }

  async createTest(insertTest: InsertTest): Promise<Test> {
    const [test] = await db
      .insert(tests)
      .values(insertTest)
      .returning();
    return test;
  }

  async getTestResults(): Promise<TestResult[]> {
    return await db.select().from(testResults);
  }

  async getTestResultsByTestId(testId: number): Promise<TestResult[]> {
    return await db.select().from(testResults).where(eq(testResults.testId, testId));
  }

  async getTestResultsByStudentId(studentId: number): Promise<TestResult[]> {
    return await db.select().from(testResults).where(eq(testResults.studentId, studentId));
  }

  async createTestResult(insertTestResult: InsertTestResult): Promise<TestResult> {
    const [testResult] = await db
      .insert(testResults)
      .values(insertTestResult)
      .returning();
    return testResult;
  }

  async getStudentReports(): Promise<StudentReport[]> {
    return await db.select().from(studentReports);
  }

  async getReportsByStudentId(studentId: number): Promise<StudentReport[]> {
    return await db.select().from(studentReports).where(eq(studentReports.studentId, studentId));
  }

  async createStudentReport(insertReport: InsertStudentReport): Promise<StudentReport> {
    const [report] = await db
      .insert(studentReports)
      .values(insertReport)
      .returning();
    return report;
  }
}

// Create database storage and seed with demo data
export const storage = new DatabaseStorage();

// Seed demo data on startup
async function seedDemoData() {
  try {
    // Check if data already exists
    const existingUsers = await storage.getUsers();
    if (existingUsers.length > 0) {
      console.log("Demo data already exists, skipping seed");
      return;
    }

    console.log("Seeding demo data...");

    // Create demo users with bcrypt hashed passwords
    const parentUser = await storage.createUser({
      fullName: "Mr. Adebayo Kunle",
      email: "daddy",
      passwordHash: await bcrypt.hash("mummy", 10),
      role: "parent"
    });

    const teacherUser = await storage.createUser({
      fullName: "Mrs. Sarah Johnson",
      email: "headmaster",
      passwordHash: await bcrypt.hash("jss6334", 10),
      role: "teacher"
    });

    const adminUser = await storage.createUser({
      fullName: "Dr. Michael Brown",
      email: "administrator",
      passwordHash: await bcrypt.hash("okokomaiko", 10),
      role: "admin"
    });

    // Create teacher record
    const teacher = await storage.createTeacher({
      userId: teacherUser.id,
      staffId: "TCH001"
    });

    // Create classes
    const class9A = await storage.createClass({
      name: "Grade 9-A",
      academicYear: "2023/2024"
    });

    const class8B = await storage.createClass({
      name: "Grade 8-B",
      academicYear: "2023/2024"
    });

    // Create subjects
    const mathSubject = await storage.createSubject({
      name: "Mathematics",
      description: "Advanced Mathematics",
      teacherId: teacher.id
    });

    const englishSubject = await storage.createSubject({
      name: "English Language",
      description: "English Language and Literature",
      teacherId: teacher.id
    });

    // Create students
    const student1 = await storage.createStudent({
      fullName: "Adebayo Benjamin",
      dob: "2008-05-15",
      gender: "Male",
      parentId: parentUser.id,
      isActive: true
    });

    const student2 = await storage.createStudent({
      fullName: "Fatima Okafor",
      dob: "2009-03-22",
      gender: "Female",
      parentId: parentUser.id,
      isActive: true
    });

    const student3 = await storage.createStudent({
      fullName: "Chioma Nwosu",
      dob: "2008-08-12",
      gender: "Female",
      parentId: parentUser.id,
      isActive: true
    });

    // Enroll students in classes
    await storage.createClassEnrollment({
      studentId: student1.id,
      classId: class9A.id
    });

    await storage.createClassEnrollment({
      studentId: student2.id,
      classId: class8B.id
    });

    await storage.createClassEnrollment({
      studentId: student3.id,
      classId: class9A.id
    });

    // Assign subjects to classes
    await storage.createSubjectClass({
      classId: class9A.id,
      subjectId: mathSubject.id
    });

    await storage.createSubjectClass({
      classId: class8B.id,
      subjectId: englishSubject.id
    });

    // Create tests
    const mathTest = await storage.createTest({
      subjectId: mathSubject.id,
      classId: class9A.id,
      title: "Mid-term Mathematics Test",
      date: "2024-01-10",
      totalScore: 100,
      gradeScale: JSON.stringify({ A: 80, B: 70, C: 60, D: 50, F: 0 })
    });

    // Create test results
    await storage.createTestResult({
      testId: mathTest.id,
      studentId: student1.id,
      score: 85,
      grade: "A"
    });

    // Create attendance records
    await storage.createAttendance({
      studentId: student1.id,
      subjectId: mathSubject.id,
      classId: class9A.id,
      date: "2024-01-15",
      status: "present"
    });

    console.log("Demo data seeded successfully");
  } catch (error) {
    console.error("Error seeding demo data:", error);
  }
}

// Seed data when module loads
seedDemoData();
